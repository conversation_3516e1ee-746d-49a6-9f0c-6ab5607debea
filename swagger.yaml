openapi: 3.0.3
info:
  title: CTF Platform API
  description: API for the CTF (Capture The Flag) Platform
  version: 1.0.0
  contact:
    name: CTF Platform Team
servers:
  - url: http://localhost:3000/api
    description: Development server
  - url: https://your-domain.com/api
    description: Production server

paths:
  /health:
    get:
      summary: Health check endpoint
      description: Returns the health status of the API
      tags:
        - Health
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "OK"

  /items:
    get:
      summary: Get team items
      description: Retrieves all items for a specific team
      tags:
        - Items
      parameters:
        - name: teamId
          in: query
          required: true
          description: The ID of the team
          schema:
            type: string
            example: "team-123"
      responses:
        '200':
          description: Successfully retrieved team items
          content:
            application/json:
              schema:
                oneOf:
                  - type: array
                    items:
                      $ref: '#/components/schemas/TeamItem'
                  - $ref: '#/components/schemas/Error'
        '400':
          description: Bad request - missing team ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /flags:
    get:
      summary: Get flags
      description: Retrieves flags for a team or all flags if no team ID provided
      tags:
        - Flags
      parameters:
        - name: teamId
          in: query
          required: false
          description: The ID of the team (optional)
          schema:
            type: string
            example: "team-123"
      responses:
        '200':
          description: Successfully retrieved flags
          content:
            application/json:
              schema:
                oneOf:
                  - type: array
                    items:
                      $ref: '#/components/schemas/Flag'
                  - $ref: '#/components/schemas/Error'

    post:
      summary: Submit a flag
      description: Submits a flag for validation and adds the corresponding item to the team
      tags:
        - Flags
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - teamId
                - flag
              properties:
                teamId:
                  type: string
                  description: The ID of the team
                  example: "team-123"
                flag:
                  type: string
                  description: The flag to submit
                  example: "CTF{example_flag}"
      responses:
        '200':
          description: Flag submitted successfully
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    properties:
                      name:
                        type: string
                        description: Name of the flag
                      points:
                        type: number
                        description: Points awarded for the flag
                      flag:
                        type: string
                        description: The submitted flag
                      itemId:
                        type: string
                        description: ID of the item added to the team
                  - $ref: '#/components/schemas/Error'
                  - type: object
                    properties:
                      status:
                        type: string
                        example: "already sent"
        '400':
          description: Bad request - missing team ID or flag
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /board:
    post:
      summary: Manage board connections
      description: Connect or disconnect items on the board
      tags:
        - Board
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BoardRequest'
      responses:
        '200':
          description: Board actions completed
          content:
            application/json:
              schema:
                oneOf:
                  - type: array
                    items:
                      $ref: '#/components/schemas/BoardResponse'
                  - $ref: '#/components/schemas/Error'
                  - type: object
                    properties:
                      status:
                        type: string
                        example: "no connection"
        '400':
          description: Bad request - missing team ID or items
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "No team ID provided"

    Flag:
      type: object
      properties:
        name:
          type: string
          description: Name of the flag
          example: "Web Challenge 1"
        flag:
          type: string
          description: The flag value (optional, may not be present in responses)
          example: "CTF{example_flag}"
        points:
          type: number
          description: Points awarded for this flag
          example: 100
        phase:
          type: string
          description: Current phase of the flag
          example: "active"

    TeamItem:
      type: object
      properties:
        tda_ctf_item:
          type: object
          properties:
            boardPosition:
              type: number
              description: Position of the item on the board
              example: 1
            components:
              type: array
              items:
                type: object
              description: Components associated with the item

    Item:
      type: object
      properties:
        documentId:
          type: string
          description: Unique identifier for the item
          example: "item-123"

    ConnectionAction:
      type: object
      properties:
        source:
          $ref: '#/components/schemas/Item'
        target:
          $ref: '#/components/schemas/Item'
        action:
          type: string
          enum: [connect, disconnect]
          description: Action to perform on the connection

    BoardRequest:
      type: object
      required:
        - teamId
        - items
      properties:
        teamId:
          type: string
          description: The ID of the team
          example: "team-123"
        items:
          type: array
          items:
            $ref: '#/components/schemas/ConnectionAction'
          description: List of connection actions to perform

    BoardResponse:
      type: object
      properties:
        status:
          type: string
          description: Status of the action
          example: "removed"
        documentId:
          type: string
          description: ID of the affected connection
          example: "connection-123"
        error:
          type: string
          description: Error message if action failed
          example: "Invalid action"
        action:
          type: string
          enum: [connect, disconnect]
          description: The action that was performed
        newItems:
          type: array
          items:
            type: string
          description: IDs of new items created (for connect actions)

tags:
  - name: Health
    description: Health check operations
  - name: Items
    description: Team items management
  - name: Flags
    description: Flag submission and retrieval
  - name: Board
    description: Board connection management
