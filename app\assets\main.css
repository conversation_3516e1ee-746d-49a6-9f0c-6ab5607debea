@import 'tailwindcss';

@theme {
  --font-*: initial;
  --font-sans:
    Inter Tight, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-mono:
    Inconsolata, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    'Liberation Mono', 'Courier New', monospace;

  --breakpoint-*: initial;
  --breakpoint-xs: 375px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --color-tda-100: #f19e18;
  --color-tda-200: #ef8a17;
  --color-tda-300: #ed7517;
  --color-tda-400: #ec6116;
  --color-tda-500: #ea4c15;
  --color-tda-600: #e83715;
  --color-tda-700: #e62314;
  --color-tda: #ef8a17;

  --color-informative: #ffffff;
  --color-background: #0b0f17;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer utilities {
  body {
    @apply bg-background text-informative;
  }
}