import { addItemToTheTeam, getItemFromConnection, getTeamConnections } from "./strapi";

async function checkConnections(teamId: string, itemId: string) {
  const visitedItems = new Set<string>();
  const connectedItems: string[] = [itemId];
  const queue: string[] = [itemId];

  visitedItems.add(itemId);

  while (queue.length > 0) {
    const currentItemId = queue.shift();
    if (!currentItemId) continue;

    const connections = await getTeamConnections(teamId, currentItemId);
    if (!connections || connections.error) {
      continue;
    }

    for (const connection of connections) {
      if (!connection.items || !Array.isArray(connection.items)) {
        continue;
      }

      for (const item of connection.items) {
        const itemDocumentId = item.documentId;

        if (itemDocumentId === currentItemId) {
          continue;
        }

        if (visitedItems.has(itemDocumentId)) {
          continue;
        }

        visitedItems.add(itemDocumentId);
        queue.push(itemDocumentId);
        connectedItems.push(itemDocumentId);
      }
    }
  }

  return connectedItems;
}

async function checkItems(teamId: string, itemId: string): Promise<string[]> {
  const newItems: string[] = [];
  const connectedItems = await checkConnections(teamId, itemId);
  console.log(connectedItems);
  const r = await getItemFromConnection(connectedItems);

  if ("error" in r) {
    return [];
  }

  for (const item of r) {
    const rI = await addItemToTheTeam(teamId, item.documentId);
    if ("error" in rI) {
      console.log("Error adding item:", rI.error);
      continue;
    }
    if ("status" in rI && rI.status === "already in team") {
      continue;
    }
    if (!("status" in rI) && !("error" in rI)) {
      console.log(rI);
      newItems.push(rI.tda_ctf_item.documentId);
      console.log("Added item to the team");
    }
  }

  return newItems;
}

export { checkItems };
