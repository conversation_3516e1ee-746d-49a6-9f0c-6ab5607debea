import { addItemToTheTeam, getItemFromConnection, getTeamConnections } from "./strapi";

async function checkConnections(teamId: string, itemId: string) {
  const visitedItems = new Set<string>(); // Track visited items to avoid cycles
  const connectedItems: string[] = [itemId]; // Store all connected item IDs, starting with source
  const queue: string[] = [itemId]; // Queue for BFS traversal

  // Add the starting item to visited set
  visitedItems.add(itemId);

  while (queue.length > 0) {
    const currentItemId = queue.shift();
    if (!currentItemId) continue; // Safety check

    // Get all connections for the current item
    const connections = await getTeamConnections(teamId, currentItemId);
    if (!connections || connections.error) {
      continue; // Skip if no connections found
    }

    // Process each connection
    for (const connection of connections) {
      if (!connection.items || !Array.isArray(connection.items)) {
        continue; // Skip if items array is missing
      }

      // Find the other item(s) in this connection (targets)
      for (const item of connection.items) {
        const itemDocumentId = item.documentId;

        // Skip if this is the current item we're processing (not a target)
        if (itemDocumentId === currentItemId) {
          continue;
        }

        // Skip if we've already visited this item
        if (visitedItems.has(itemDocumentId)) {
          continue;
        }

        // Add to visited set and queue for further exploration
        visitedItems.add(itemDocumentId);
        queue.push(itemDocumentId);
        connectedItems.push(itemDocumentId);
      }
    }
  }

  return connectedItems;
}

async function checkItems(teamId: string, itemId: string): Promise<string[]> {
  const newItems: string[] = [];
  const connectedItems = await checkConnections(teamId, itemId);
  console.log(connectedItems);
  const r = await getItemFromConnection(connectedItems);

  if (!r || r.error) {
    return r;
  }

  for (const item of r) {
    const rI = await addItemToTheTeam(teamId, item.documentId);
    if (rI.status !== "already in team") {
        console.log(rI);
      newItems.push(rI.tda_ctf_item.documentId);
      console.log("Added item to the team");
    }
  }

  return newItems;
}

export { checkItems };
