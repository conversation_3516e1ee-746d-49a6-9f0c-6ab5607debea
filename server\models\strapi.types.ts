export interface IStrapiResponse {
  data: {
    data: any[];
    meta: {
      pagination: {
        total: number;
      };
    };
  };
}

export interface IFlag {
  name: string;
  flag?: string;
  points: number;
  phase: string;
}

export interface ITeamItems {
  tda_ctf_item: {
    boardPosition: number;
    components: any[];
  };
}

export interface ITeam {
  documentId: string;
  name: string;
}

export interface IItem {
  documentId: string;
  boardPosition: number;
  name: string;
  tda_ctf_items: {
    documentId: string;
  }[];
}

export interface IConnections {
  documentId: string;
  team: ITeam;
  items: IItem[];
}

export interface ITeamItem {
  documentId: string;
  tda_core_team: {
    documentId: string;
  };
  tda_ctf_item: {
    documentId: string;
  };
}
