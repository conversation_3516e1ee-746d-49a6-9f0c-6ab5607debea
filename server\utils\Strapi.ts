import axios from "axios";
import qs from "qs";
import type { connectionAction, IBoardFunctionResponse } from "../models/board.types";
import type {
  IStrapiResponse,
  IFlag,
  ITeamItems,
  IItem,
  IConnections,
  ITeamItem,
} from "../models/strapi.types";

function formatComponentString(dynamicZone: any, componentPrefix: string): any {
  const newDynamicZone = dynamicZone.map((c: any) => {
    c.__component = c.__component.replace(`${componentPrefix}.`, "");
    return c;
  });

  return newDynamicZone;
}

async function strapiRequest(
  endpoint: string,
  query: object,
): Promise<IStrapiResponse | { error: string }> {
  const qsSlug = qs.stringify(query, {
    encodeValuesOnly: true,
  });
  const r: IStrapiResponse = await axios.get(`${process.env.CMS_URL}/api/${endpoint}?${qsSlug}`, {
    headers: {
      Authorization: `Bearer ${process.env.CMS_KEY}`,
    },
  });

  if (r.data.meta.pagination.total === 0) {
    return {
      error: "Not Found",
    };
  }

  return r;
}

async function getFlags(): Promise<IFlag[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-flags", {
    populate: {
      tda_core_phase: {
        fields: ["state"],
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  const flags = r.data.data.map(
    (d: { name: string; points: number; tda_core_phase: { state: string } }) => ({
      name: d.name,
      points: d.points,
      phase: d.tda_core_phase.state,
    }),
  );

  return flags;
}

async function getTeamItems(teamId: string): Promise<ITeamItems[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-team-items", {
    populate: {
      tda_core_team: {
        fields: ["name"],
      },
      tda_ctf_item: {
        fields: ["*"],
        populate: ["components"],
      },
    },
    filters: {
      tda_core_team: {
        documentId: {
          $eq: teamId,
        },
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  // Transform data by extracting only needed properties
  const cleanedData = r.data.data.map((d) => ({
    tda_ctf_item: {
      boardPosition: d.tda_ctf_item.boardPosition,
      components: formatComponentString(d.tda_ctf_item.components, "td-a-ctf-platform-items"),
    },
  }));

  return cleanedData;
}

async function getTeamFlags(teamId: string): Promise<IFlag[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-team-flags", {
    populate: {
      tda_core_team: {
        fields: ["name"],
      },
      tda_ctf_flag: {
        fields: ["*"],
        populate: ["tda_core_phase"],
      },
    },
    filters: {
      tda_core_team: {
        documentId: {
          $eq: teamId,
        },
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  const cleanedData = r.data.data.map((d) => ({
    name: d.tda_ctf_flag.name,
    flag: d.tda_ctf_flag.flag,
    points: d.tda_ctf_flag.points,
    phase: d.tda_ctf_flag.tda_core_phase.state,
  }));

  return cleanedData;
}

async function getConnections(
  teamId: string,
  item: connectionAction,
): Promise<IConnections | { error: string }> {
  const r = await strapiRequest("tda-ctf-connections", {
    populate: {
      team: {
        fields: ["name"],
      },
      items: {
        fields: ["*"],
      },
    },
    filters: {
      $and: [
        {
          items: {
            documentId: {
              $eq: item.source.documentId,
            },
          },
        },
        {
          items: {
            documentId: {
              $eq: item.target.documentId,
            },
          },
        },
      ],
    },
    team: {
      documentId: {
        $eq: teamId,
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  return r.data.data[0];
}

async function removeConnection(connectionId: string) {
  const r = await axios.delete(`${process.env.CMS_URL}/api/tda-ctf-connections/${connectionId}`, {
    headers: {
      Authorization: `Bearer ${process.env.CMS_KEY}`,
    },
  });
  if (r.status === 200) {
    console.log("Connection removed");
    return r.data;
  }
  return r;
}

async function createConnection(teamId: string, items: connectionAction) {
  const r = await axios.post(
    `${process.env.CMS_URL}/api/tda-ctf-connections`,
    {
      data: {
        team: teamId,
        items: [items.source.documentId, items.target.documentId],
      },
    },
    {
      headers: {
        Authorization: `Bearer ${process.env.CMS_KEY}`,
      },
    },
  );

  if (r.status > 399) {
    console.log("Error creating connection");
    return r;
  }

  const bR = await strapiRequest("tda-ctf-boards", {
    populate: {
      team: {
        fields: ["name"],
      },
      connections: {
        fields: ["*"],
      },
    },
    filters: {
      team: {
        documentId: {
          $eq: teamId,
        },
      },
    },
  });

  if ("error" in bR) {
    console.log("Error getting board");
    return r;
  }

  const oldConnections: IBoardFunctionResponse[] = bR.data.data[0].connections
    .map((c: IBoardFunctionResponse) => ({ documentId: c.documentId }))
    .filter((c: IBoardFunctionResponse) => c.documentId && c.documentId !== "");
  oldConnections.push({ documentId: r.data.data.documentId });

  await axios.put(
    `${process.env.CMS_URL}/api/tda-ctf-boards/${bR.data.data[0].documentId}`,
    {
      data: {
        team: teamId,
        connections: {
          connect: oldConnections,
        },
      },
    },
    {
      headers: {
        Authorization: `Bearer ${process.env.CMS_KEY}`,
      },
    },
  );

  return r;
}

async function getTeamConnections(
  teamId: string,
  itemId: string,
): Promise<IConnections[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-connections", {
    populate: {
      team: {
        fields: ["name"],
      },
      items: {
        fields: ["*"],
      },
    },
    filters: {
      items: {
        documentId: {
          $eq: itemId,
        },
      },
    },
    team: {
      documentId: {
        $eq: teamId,
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  return r.data.data;
}

//TODO: Need to by typed
async function getBoard(teamId: string): Promise<any | { error: string }> {
  const r = await strapiRequest("tda-ctf-boards", {
    populate: {
      team: {
        fields: ["name"],
      },
      connections: {
        fields: ["*"],
      },
    },
    filters: {
      team: {
        documentId: {
          $eq: teamId,
        },
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  return r.data.data[0];
}

async function getItemFromConnection(
  connectionIds: string[],
): Promise<IItem[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-items", {
    populate: {
      tda_ctf_items: {
        fields: "documentId",
      },
    },
    filters: {
      tda_ctf_items: {
        documentId: {
          $in: connectionIds,
        },
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  return r.data.data;
}

async function addItemToTheTeam(
  teamId: string,
  itemId: string,
): Promise<ITeamItem | { status: string } | { error: string }> {
  const tempR = await strapiRequest("tda-ctf-team-items", {
    populate: {
      tda_core_team: {
        fields: ["name"],
      },
      tda_ctf_item: {
        fields: ["documentId"],
      },
    },
    filters: {
      tda_core_team: {
        documentId: {
          $eq: teamId,
        },
      },
      tda_ctf_item: {
        documentId: {
          $eq: itemId,
        },
      },
    },
  });

  if (!("error" in tempR) && tempR.data.data.length > 0) {
    console.log("Item already in team");
    return {
      status: "already in team",
    };
  }

  const qsSlug = qs.stringify({
    populate: {
      tda_core_team: {
        fields: ["name"],
      },
      tda_ctf_item: {
        fields: ["documentId"],
      },
    },
  });

  const r = await axios.post(
    `${process.env.CMS_URL}/api/tda-ctf-team-items?${qsSlug}`,
    {
      data: {
        tda_core_team: teamId,
        tda_ctf_item: itemId,
      },
    },
    {
      headers: {
        Authorization: `Bearer ${process.env.CMS_KEY}`,
      },
    },
  );

  if (r.status > 399) {
    console.log("Error adding item to the team");
    return {
      error: "Error adding item to the team",
    };
  }

  return r.data.data;
}

async function sendFlag(teamId: string, flag: string): Promise<any | { error: string }> {
  const tempR = await strapiRequest("tda-ctf-team-flags", {
    populate: {
      tda_core_team: {
        fields: ["name"],
      },
      tda_ctf_flag: {
        fields: ["flag"],
      },
    },
    filters: {
      tda_core_team: {
        documentId: {
          $eq: teamId,
        },
      },
      tda_ctf_flag: {
        flag: {
          $eq: flag,
        },
      },
    },
  });

  if (!("error" in tempR) && tempR.data.data.length > 0) {
    console.log("Flag already sent");
    return {
      status: "already sent",
    };
  }

  const currentPhase = await axios.get(`${process.env.CMS_URL}/api/tda-core-phase/current`, {
    headers: {
      Authorization: `Bearer ${process.env.CMS_KEY}`,
    },
  });

  if (currentPhase.data.length === 0) {
    return {
      error: "No current phase",
    };
  }

  const flagR = await strapiRequest("tda-ctf-flags", {
    populate: {
      tda_core_phase: {
        fields: ["documentId", "state"],
      },
    },
    filters: {
      flag: {
        $eq: flag,
      },
      tda_core_phase: {
        state: {
          $eq: currentPhase.data[0].state,
        },
      },
    },
  });

  if ("error" in flagR) {
    return flagR as { error: string };
  }

  const qsSlug = qs.stringify(
    {
      populate: {
        tda_core_team: {
          fields: ["name"],
        },
        tda_ctf_flag: {
          fields: ["name", "flag", "points"],
        },
      },
    },
    {
      encodeValuesOnly: true,
    },
  );

  const r = await axios.post(
    `${process.env.CMS_URL}/api/tda-ctf-team-flags?${qsSlug}`,
    {
      data: {
        tda_core_team: teamId,
        tda_ctf_flag: flagR.data.data[0].documentId,
      },
    },
    {
      headers: {
        Authorization: `Bearer ${process.env.CMS_KEY}`,
      },
    },
  );

  if (r.status > 399) {
    console.log("Error sending flag");
    return {
      error: "Error sending flag",
    };
  }

  return r.data.data;
}

async function getFlagItem(flagId: string): Promise<any | { error: string }> {
  const r = await strapiRequest("tda-ctf-items", {
    populate: {
      tda_ctf_flag: {
        fields: "name",
      },
    },
    filters: {
      tda_ctf_flag: {
        documentId: {
          $eq: flagId,
        },
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  return r.data.data[0];
}

export {
  getFlags,
  getTeamItems,
  getTeamFlags,
  getConnections,
  removeConnection,
  createConnection,
  getTeamConnections,
  getBoard,
  getItemFromConnection,
  addItemToTheTeam,
  sendFlag,
  getFlagItem,
};
