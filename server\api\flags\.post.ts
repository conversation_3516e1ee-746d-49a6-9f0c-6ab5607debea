import { getFlagItem, sendFlag } from "~~/server/utils/strapi";

export default defineEventHandler(async (event) => {
  const { teamId, flag } = await readBody(event);
  if (!teamId || !flag) {
    return {
      error: "No team ID or flag provided",
    };
  }

  const r = await sendFlag(teamId, flag);
  if ("error" in r || "status" in r) {
    return r;
  }

  const flagItem = await getFlagItem(r.tda_ctf_flag.documentId);

  if ("error" in flagItem) {
    return flagItem;
  }

  const teamItem = await addItemToTheTeam(teamId, flagItem.documentId);

  if ("error" in teamItem || "status" in teamItem) {
    return teamItem;
  }

  return {
    name: r.tda_ctf_flag.name,
    points: r.tda_ctf_flag.points,
    flag: r.tda_ctf_flag.flag,
    itemId: teamItem.tda_ctf_item.documentId,
  };
});
