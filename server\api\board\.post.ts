import type {
  connectionAction,
  IBoardFunctionResponse,
  IBoardRequest,
  IBoardResponse,
} from "~~/server/models/board.types";
import { checkItems } from "~~/server/utils/connections";
import { createConnection, getConnections, removeConnection } from "~~/server/utils/strapi";

async function disconnectItems(
  teamId: string,
  items: connectionAction,
): Promise<IBoardFunctionResponse | { status: string }> {
  const connection = await getConnections(teamId, items);
  if (!connection || "error" in connection) {
    return { status: "no connection" };
  }
  await removeConnection(connection.documentId);
  console.log("removed connection");
  return { status: "removed" };
}

async function connectItems(
  teamId: string,
  items: connectionAction,
): Promise<IBoardFunctionResponse | { status: string }> {
  const connection = await getConnections(teamId, items);

  if (!connection || "error" in connection) {
    console.log("No connections found");
    const r = await createConnection(teamId, items);
    console.log("created connection");

    const item: string[] = await checkItems(teamId, items.source.documentId);

    return {
      newItems: item.length > 0 ? item : undefined,
      documentId: r.data.data.documentId,
    };
  }

  return await disconnectItems(teamId, items);
}

export default defineEventHandler(
  async (event): Promise<IBoardResponse[] | { status: string } | { error: string }> => {
    const body: IBoardRequest = await readBody<IBoardRequest>(event);
    const actions: IBoardResponse[] = [];
    if (!body.teamId) {
      // TODO: After keycloak implementation get teamId from keycloak and not via post req
      await setResponseStatus(event, 400);
      return {
        error: "No team ID provided",
      };
    }

    if (!body.items) {
      await setResponseStatus(event, 400);
      return {
        error: "No items provided",
      };
    }

    for (const item of body.items) {
      switch (item.action) {
        case "connect":
          actions.push({ ...(await connectItems(body.teamId, item)), action: item.action });
          break;
        case "disconnect":
          actions.push({ ...(await disconnectItems(body.teamId, item)), action: item.action });
          break;
        default:
          actions.push({ error: "Invalid action", action: item.action });
          break;
      }
    }

    return actions;
  },
);
